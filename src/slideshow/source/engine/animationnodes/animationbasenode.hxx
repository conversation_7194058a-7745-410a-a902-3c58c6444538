/* -*- Mode: C++; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/*
 * This file is part of the LibreOffice project.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * This file incorporates work covered by the following license notice:
 *
 *   Licensed to the Apache Software Foundation (ASF) under one or more
 *   contributor license agreements. See the NOTICE file distributed
 *   with this work for additional information regarding copyright
 *   ownership. The ASF licenses this file to you under the Apache
 *   License, Version 2.0 (the "License"); you may not use this file
 *   except in compliance with the License. You may obtain a copy of
 *   the License at http://www.apache.org/licenses/LICENSE-2.0 .
 */
#ifndef INCLUDED_SLIDESHOW_SOURCE_ENGINE_ANIMATIONNODES_ANIMATIONBASENODE_HXX
#define INCLUDED_SLIDESHOW_SOURCE_ENGINE_ANIMATIONNODES_ANIMATIONBASENODE_HXX

#include <com/sun/star/animations/XAnimate.hpp>

#include <basecontainernode.hxx>
#include <activitiesfactory.hxx>
#include <shapeattributelayerholder.hxx>
#include <attributableshape.hxx>
#include <shapesubset.hxx>

namespace slideshow::internal {

/** Common base class for all leaf animation nodes.

    This class basically holds the target shape
*/
class AnimationBaseNode : public BaseNode
{
public:
    AnimationBaseNode(
        css::uno::Reference<css::animations::XAnimationNode> const& xNode,
        BaseContainerNodeSharedPtr const& pParent,
        NodeContext const& rContext );

#if defined(DBG_UTIL)
    virtual void showState() const override;
#endif
    virtual void removeEffect() override;

protected:
    virtual void dispose() override;

    css::uno::Reference<css::animations::XAnimate> const& getXAnimateNode() const
        { return mxAnimateNode; }

    /// Create parameter struct for ActivitiesFactory
    ActivitiesFactory::CommonParameters fillCommonParameters() const;
    ::basegfx::B2DVector const&         getSlideSize() const { return maSlideSize; }
    AttributableShapeSharedPtr const &  getShape() const;

private:
    virtual bool hasPendingAnimation() const override;
    virtual bool enqueueActivity() const;

private: // state transition callbacks
    virtual bool init_st() override;
    virtual bool resolve_st() override;
    virtual void activate_st() override;
    virtual void deactivate_st( NodeState eDestState ) override;
    virtual AnimationActivitySharedPtr createActivity() const = 0;

private:
    /** Returns true, if this is a subset animation, and
        the subset is autogenerated (e.g. from an
        iteration)
    */
    bool isDependentSubsettedShape() const
        { return mpShapeSubset && !mbIsIndependentSubset; }

private:
    css::uno::Reference<css::animations::XAnimate>  mxAnimateNode;
    ShapeAttributeLayerHolder                       maAttributeLayerHolder;
    ::basegfx::B2DVector                            maSlideSize;

    /// When valid, this node has a plain target shape
    AttributableShapeSharedPtr                      mpShape;
    /// When valid, this is a subsetted target shape
    ShapeSubsetSharedPtr                            mpShapeSubset;
    SubsettableShapeManagerSharedPtr                mpSubsetManager;
    bool                                            mbPreservedVisibility;
    bool                                            mbIsIndependentSubset;

protected:
    AnimationActivitySharedPtr                      mpActivity;
};

} // namespace presentation::internal

#endif // INCLUDED_SLIDESHOW_SOURCE_ENGINE_ANIMATIONNODES_ANIMATIONBASENODE_HXX

/* vim:set shiftwidth=4 softtabstop=4 expandtab: */
