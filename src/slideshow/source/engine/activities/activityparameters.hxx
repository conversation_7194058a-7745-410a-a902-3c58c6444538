/* -*- Mode: C++; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/*
 * This file is part of the LibreOffice project.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * This file incorporates work covered by the following license notice:
 *
 *   Licensed to the Apache Software Foundation (ASF) under one or more
 *   contributor license agreements. See the NOTICE file distributed
 *   with this work for additional information regarding copyright
 *   ownership. The ASF licenses this file to you under the Apache
 *   License, Version 2.0 (the "License"); you may not use this file
 *   except in compliance with the License. You may obtain a copy of
 *   the License at http://www.apache.org/licenses/LICENSE-2.0 .
 */

#ifndef INCLUDED_SLIDESHOW_SOURCE_ENGINE_ACTIVITIES_ACTIVITYPARAMETERS_HXX
#define INCLUDED_SLIDESHOW_SOURCE_ENGINE_ACTIVITIES_ACTIVITYPARAMETERS_HXX

#include <event.hxx>
#include <eventqueue.hxx>
#include <expressionnode.hxx>
#include <wakeupevent.hxx>

#include <optional>
#include <vector>

namespace slideshow::internal {

/** Parameter struct for animation activities

    This struct contains all common parameters needed to
    initialize the activities generated by the ActivityFactory.
*/
struct ActivityParameters
{
    /** Create

        @param rEndEvent
        Event to be fired, when the activity ends.

        @param rEventQueue
        Queue to add end event to

        @param nMinDuration
        Minimal duration of the activity (might actually be
        longer because of nMinNumberOfFrames). Note that this
        duration must always be the <em>simple</em> duration,
        i.e. without any repeat.

        @param rRepeats
        Number of repeats. If this parameter is invalid,
        infinite repeat is assumed.

        @param nAccelerationFraction
        Value between 0 and 1, denoting the fraction of the
        total simple duration, which the animation should
        accelerate.

        @param nDecelerationFraction
        Value between 0 and 1, denoting the fraction of the
        total simple duration, which the animation should
        decelerate. Note that the ranges
        [0,nAccelerationFraction] and
        [nDecelerationFraction,1] must be non-overlapping!

        @param bAutoReverse
        When true, at the end of the simple duration, the
        animation plays reversed to the start value. Note that
        nMinDuration still specifies the simple duration,
        i.e. when bAutoReverse is true, the implicit duration
        doubles.
    */
    ActivityParameters(
        const EventSharedPtr&                       rEndEvent,
        EventQueue&                                 rEventQueue,
        ActivitiesQueue&                            rActivitiesQueue,
        double                                      nMinDuration,
        ::std::optional<double> const&            rRepeats,
        double                                      nAccelerationFraction,
        double                                      nDecelerationFraction,
        sal_uInt32                                  nMinNumberOfFrames,
        bool                                        bAutoReverse )
        : mrEndEvent( rEndEvent ),
          mpWakeupEvent(),
          mrEventQueue( rEventQueue ),
          mrActivitiesQueue( rActivitiesQueue ),
          mpFormula(),
          maDiscreteTimes(),
          mnMinDuration( nMinDuration ),
          mrRepeats( rRepeats ),
          mnAccelerationFraction( nAccelerationFraction ),
          mnDecelerationFraction( nDecelerationFraction ),
          mnMinNumberOfFrames( nMinNumberOfFrames ),
          mbAutoReverse( bAutoReverse ) {}

    /// End event to fire, when activity is over
    const EventSharedPtr&                       mrEndEvent;
    /// Wakeup event to use for discrete activities
    WakeupEventSharedPtr                        mpWakeupEvent;

    /// EventQueue to add events to
    EventQueue&                                 mrEventQueue;

    /// ActivitiesQueue to add events to
    ActivitiesQueue&                            mrActivitiesQueue;

    /// Optional formula
    std::shared_ptr<ExpressionNode>                     mpFormula;

    /// Key times, for discrete and key time activities
    ::std::vector< double >                     maDiscreteTimes;

    /// Total duration of activity (including all repeats)
    const double                                mnMinDuration;
    ::std::optional<double> const&            mrRepeats;
    const double                                mnAccelerationFraction;
    const double                                mnDecelerationFraction;

    /// Minimal number of frames this activity must render
    const sal_uInt32                            mnMinNumberOfFrames;

    /// When true, activity is played reversed after mnDuration.
    const bool                                  mbAutoReverse;
};

} // namespace presentation::internal

#endif // INCLUDED_SLIDESHOW_SOURCE_ENGINE_ACTIVITIES_ACTIVITYPARAMETERS_HXX

/* vim:set shiftwidth=4 softtabstop=4 expandtab: */
