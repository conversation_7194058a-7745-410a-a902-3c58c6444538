/* -*- Mode: C++; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/*
 * This file is part of the LibreOffice project.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * This file incorporates work covered by the following license notice:
 *
 *   Licensed to the Apache Software Foundation (ASF) under one or more
 *   contributor license agreements. See the NOTICE file distributed
 *   with this work for additional information regarding copyright
 *   ownership. The ASF licenses this file to you under the Apache
 *   License, Version 2.0 (the "License"); you may not use this file
 *   except in compliance with the License. You may obtain a copy of
 *   the License at http://www.apache.org/licenses/LICENSE-2.0 .
 */

#ifndef INCLUDED_SLIDESHOW_SOURCE_ENGINE_TRANSITIONS_PARAMETRICPOLYPOLYGON_HXX
#define INCLUDED_SLIDESHOW_SOURCE_ENGINE_TRANSITIONS_PARAMETRICPOLYPOLYGON_HXX

#include <basegfx/polygon/b2dpolypolygon.hxx>
#include <memory>


/* Definition of ParametricPolyPolygon interface */

namespace slideshow::internal
    {
        /** Interface defining a parametric poly-polygon.

            This interface defines a poly-polygon, whose actual shape
            is parameterized by a floating point value. This is
            e.g. used to generically access the various clip polygon
            generators for transition effects.

            Since for every parametric poly-polygon, there is a set of
            variations, which can easily be generated by simple
            transformations or change in parameter range sweep
            direction, objects implementing this interface only
            generate <em>one</em> prototypical instance of the
            parametric poly-polygon. Generally speaking, the main
            effect direction should be horizontal, it should make
            increasingly more area visible (transition 'in'), and when
            there is a designated direction given, that should be
            left-to-right.
         */
        class ParametricPolyPolygon
        {
        public:
            virtual ~ParametricPolyPolygon() {}

            /** Retrieve the poly-polygon for value t.

                @param t
                Current parameter value to retrieve the corresponding
                poly-polygon for. Permissible values for t must be in
                the range [0,1].

                @return a poly-polygon corresponding to the given
                parameter value. The poly-polygon is interpreted as
                living in the unit rectangle (i.e. [0,1]x[0,1]), but
                is not necessarily constrained to completely lie in
                this area (this very much depends on the actual effect
                to be generated). Although, from a performance
                perspective, it currently <em>is</em> advantageous to
                try to keep the poly-polygon within these bounds (at
                least if there are no hard reasons not to do so),
                because then reversion or out transformations are
                potentially faster to compute (see the
                TransitionInfo::meReverseMethod member in
                transitionfactory.cxx). Furthermore, if one of the
                polygon modifications involve subtraction (also see
                TransitionInfo::meReverseMethod), all generated
                polygons should be oriented clock-wise
                (i.e. traversing the polygon vertices with increasing
                vertex index should generate a clock-wise movement).
             */
            virtual ::basegfx::B2DPolyPolygon operator()( double t ) = 0;
        };

        typedef ::std::shared_ptr< ParametricPolyPolygon > ParametricPolyPolygonSharedPtr;

}

#endif // INCLUDED_SLIDESHOW_SOURCE_ENGINE_TRANSITIONS_PARAMETRICPOLYPOLYGON_HXX

/* vim:set shiftwidth=4 softtabstop=4 expandtab: */
