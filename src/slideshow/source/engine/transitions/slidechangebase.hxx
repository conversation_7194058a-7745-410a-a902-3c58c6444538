/* -*- Mode: C++; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/*
 * This file is part of the LibreOffice project.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * This file incorporates work covered by the following license notice:
 *
 *   Licensed to the Apache Software Foundation (ASF) under one or more
 *   contributor license agreements. See the NOTICE file distributed
 *   with this work for additional information regarding copyright
 *   ownership. The ASF licenses this file to you under the Apache
 *   License, Version 2.0 (the "License"); you may not use this file
 *   except in compliance with the License. You may obtain a copy of
 *   the License at http://www.apache.org/licenses/LICENSE-2.0 .
 */

#ifndef INCLUDED_SLIDESHOW_SOURCE_ENGINE_TRANSITIONS_SLIDECHANGEBASE_HXX
#define INCLUDED_SLIDESHOW_SOURCE_ENGINE_TRANSITIONS_SLIDECHANGEBASE_HXX

#include <unoview.hxx>
#include <utility>
#include <vieweventhandler.hxx>
#include <numberanimation.hxx>
#include <slide.hxx>
#include <screenupdater.hxx>
#include <soundplayer.hxx>

#include <memory>
#include <optional>

namespace cppcanvas
{
    class Canvas;
    class CustomSprite;
}

namespace slideshow::internal {

/** Base class for all slide change effects.

    This class provides the basic sprite and view handling
    functionality.  Derived classes should normally only need to
    implement the perform() method.
*/
class SlideChangeBase : public ViewEventHandler,
                        public NumberAnimation
{
public:
    SlideChangeBase(const SlideChangeBase&) = delete;
    SlideChangeBase& operator=(const SlideChangeBase&) = delete;

    // NumberAnimation
    virtual bool operator()( double x ) override;
    virtual double getUnderlyingValue() const override;

    // Animation
    virtual void prefetch() override;
    virtual void start( const AnimatableShapeSharedPtr&,
                        const ShapeAttributeLayerSharedPtr& ) override;
    virtual void end() override;

    // ViewEventHandler
    virtual void viewAdded( const UnoViewSharedPtr& rView ) override;
    virtual void viewRemoved( const UnoViewSharedPtr& rView ) override;
    virtual void viewChanged( const UnoViewSharedPtr& rView ) override;
    virtual void viewsChanged() override;

protected:
    /** Create a new SlideChanger, for the given leaving and
        entering slides.
    */
    SlideChangeBase(
        ::std::optional<SlideSharedPtr>           leavingSlide,
        const SlideSharedPtr&                     pEnteringSlide,
        SoundPlayerSharedPtr                      pSoundPlayer,
        const UnoViewContainer&                   rViewContainer,
        ScreenUpdater&                            rScreenUpdater,
        EventMultiplexer&                         rEventMultiplexer,
        bool                                      bCreateLeavingSprites = true,
        bool                                      bCreateEnteringSprites = true );

    /// Info on a per-view basis
    struct ViewEntry
    {
        explicit ViewEntry( UnoViewSharedPtr  rView ) :
            mpView(std::move( rView ))
        {
        }

        /// The view this entry is for
        UnoViewSharedPtr                              mpView;
        /// outgoing slide sprite
        std::shared_ptr<cppcanvas::CustomSprite>    mpOutSprite;
        /// incoming slide sprite
        std::shared_ptr<cppcanvas::CustomSprite>    mpInSprite;
        /// outgoing slide bitmap
        mutable SlideBitmapSharedPtr                  mpLeavingBitmap;
        /// incoming slide bitmap
        mutable SlideBitmapSharedPtr                  mpEnteringBitmap;

        // for algo access
        const UnoViewSharedPtr& getView() const { return mpView; }
    };

    typedef ::std::vector<ViewEntry> ViewsVecT;

    ViewsVecT::const_iterator beginViews() { return maViewData.begin(); }
    ViewsVecT::const_iterator endViews() { return maViewData.end(); }

    SlideBitmapSharedPtr getLeavingBitmap( const ViewEntry& rViewEntry ) const;
    SlideBitmapSharedPtr getEnteringBitmap( const ViewEntry& rViewEntry ) const;

    SlideBitmapSharedPtr createBitmap( const UnoViewSharedPtr&                pView,
                                       const std::optional<SlideSharedPtr>& rSlide_ ) const;

    ::basegfx::B2ISize getEnteringSlideSizePixel( const UnoViewSharedPtr& pView ) const;

    static void renderBitmap( SlideBitmapSharedPtr const&       pSlideBitmap,
                              cppcanvas::CanvasSharedPtr const& pCanvas );

    /** Called on derived classes to perform actions before first run.

        This typically involves rendering of the initial slide content.

        @param rViewEntry the view entry

        @param rDestinationCanvas the canvas to render on
     */
    virtual void prepareForRun(
            const ViewEntry& rViewEntry,
            const cppcanvas::CanvasSharedPtr& rDestinationCanvas );

    /** Called on derived classes to implement actual slide change.

        This method is called with the sprite of the slide coming 'in'

        @param rSprite
        Current sprite to operate on. This is the sprite of the
        'entering' slide

        @param t
        Current parameter value
    */
    virtual void performIn(
        const cppcanvas::CustomSpriteSharedPtr&   rSprite,
        const ViewEntry&                          rViewEntry,
        const cppcanvas::CanvasSharedPtr&         rDestinationCanvas,
        double                                    t );

    /** Called on derived classes to implement actual slide change.

        This method is called with the sprite of the slide moving 'out'

        @param rSprite
        Current sprite to operate on. This is the sprite of the
        'leaving' slide

        @param t
        Current parameter value
    */
    virtual void performOut(
        const cppcanvas::CustomSpriteSharedPtr&   rSprite,
        const ViewEntry&                          rViewEntry,
        const cppcanvas::CanvasSharedPtr&         rDestinationCanvas,
        double                                    t );

    ScreenUpdater& getScreenUpdater() const { return mrScreenUpdater; }

private:

    cppcanvas::CustomSpriteSharedPtr createSprite(
        UnoViewSharedPtr const &   pView,
        ::basegfx::B2DSize const & rSpriteSize,
        double                     nPrio ) const;

    void addSprites( ViewEntry& rEntry );
    static void clearViewEntry( ViewEntry& rEntry );

    SoundPlayerSharedPtr                mpSoundPlayer;

    EventMultiplexer&                   mrEventMultiplexer;
    ScreenUpdater&                      mrScreenUpdater;

    ::std::optional<SlideSharedPtr>   maLeavingSlide;
    SlideSharedPtr                      mpEnteringSlide;

    ViewsVecT                           maViewData;
    const UnoViewContainer&             mrViewContainer;

    const bool                          mbCreateLeavingSprites;
    const bool                          mbCreateEnteringSprites;
    bool                                mbSpritesVisible;
    bool                                mbFinished;
    bool                                mbPrefetched;
};

} // namespace presentation::internal

#endif // INCLUDED_SLIDESHOW_SOURCE_ENGINE_TRANSITIONS_SLIDECHANGEBASE_HXX

/* vim:set shiftwidth=4 softtabstop=4 expandtab: */
