/* -*- Mode: C++; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/*
 * This file is part of the LibreOffice project.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * This file incorporates work covered by the following license notice:
 *
 *   Licensed to the Apache Software Foundation (ASF) under one or more
 *   contributor license agreements. See the NOTICE file distributed
 *   with this work for additional information regarding copyright
 *   ownership. The ASF licenses this file to you under the Apache
 *   License, Version 2.0 (the "License"); you may not use this file
 *   except in compliance with the License. You may obtain a copy of
 *   the License at http://www.apache.org/licenses/LICENSE-2.0 .
 */


#include <basegfx/point/b2dpoint.hxx>
#include "doublediamondwipe.hxx"


namespace slideshow::internal {

::basegfx::B2DPolyPolygon DoubleDiamondWipe::operator () ( double t )
{
    // outer:
    const double a = ::basegfx::pruneScaleValue( 0.25 + (t * 0.75) );
    ::basegfx::B2DPolygon poly;
    poly.append( ::basegfx::B2DPoint( 0.5 + a, 0.5 ) );
    poly.append( ::basegfx::B2DPoint( 0.5, 0.5 - a ) );
    poly.append( ::basegfx::B2DPoint( 0.5 - a, 0.5 ) );
    poly.append( ::basegfx::B2DPoint( 0.5, 0.5 + a ) );
    poly.setClosed(true);
    ::basegfx::B2DPolyPolygon res(poly);

    // inner (reverse order to clip):
    const double b = ::basegfx::pruneScaleValue( (1.0 - t) * 0.25 );
    poly.clear();
    poly.append( ::basegfx::B2DPoint( 0.5 + b, 0.5 ) );
    poly.append( ::basegfx::B2DPoint( 0.5, 0.5 + b ) );
    poly.append( ::basegfx::B2DPoint( 0.5 - b, 0.5 ) );
    poly.append( ::basegfx::B2DPoint( 0.5, 0.5 - b ) );
    poly.setClosed(true);
    res.append(poly);

    return res;
}

}

/* vim:set shiftwidth=4 softtabstop=4 expandtab: */
