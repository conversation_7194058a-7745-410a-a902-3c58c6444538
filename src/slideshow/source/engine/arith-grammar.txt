#
# This file is part of the LibreOffice project.
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# This file incorporates work covered by the following license notice:
#
#   Licensed to the Apache Software Foundation (ASF) under one or more
#   contributor license agreements. See the NOTICE file distributed
#   with this work for additional information regarding copyright
#   ownership. The ASF licenses this file to you under the Apache
#   License, Version 2.0 (the "License"); you may not use this file
#   except in compliance with the License. You may obtain a copy of
#   the License at http://www.apache.org/licenses/LICENSE-2.0 .
#

Based on the C grammar for arithmetic expressions
=================================================

number_digit = '0'|'1'|'2'|'3'|'4'|'5'|'6'|'7'|'8'|'9'

number_exponent = 'e'|'E'

basic_number = basic_number number_digit | number_digit

number = 
	   basic_number |

	   basic_number number_exponent basic_number |	   
	   basic_number number_exponent '-' basic_number |
	   basic_number number_exponent '+' basic_number |

	   '.' basic_number number_exponent basic_number |
	   '.' basic_number number_exponent '-' basic_number |
	   '.' basic_number number_exponent '+' basic_number |

	   basic_number '.' number_exponent basic_number |
	   basic_number '.' number_exponent '-' basic_number |
	   basic_number '.' number_exponent '+' basic_number |

	   basic_number '.' basic_number number_exponent basic_number |
	   basic_number '.' basic_number number_exponent '-' basic_number |
	   basic_number '.' basic_number number_exponent '+' basic_number


identifier = '$'|'pi'|'e'|'X'|'Y'|'Width'|'Height'
              ^            ^   ^     ^       ^
              |            |   |     |       |
	 '$' in PPT            |   |     |       |
			 '#ppt_x' in PPT   |     |       |
			     '#ppt_y' in PPT     |       |
				       '#ppt_w' in PPT       |
                               '#ppt_h' in PPT

unary_function = 'abs'|'sqrt'|'sin'|'cos'|'tan'|'atan'|'acos'|'asin'|'exp'|'log'
binary_function = 'min'|'max'


basic_expression = 
				 number |
				 identifier | 
				 unary_function '(' additive_expression ')' |
				 binary_function '(' additive_expression ',' additive_expression ')' |
				 '(' additive_expression ')'

unary_expression = '-' basic_expression

multiplicative_expression = 
						  basic_expression |
						  multiplicative_expression '*' basic_expression |
						  multiplicative_expression '/' basic_expression

additive_expression = 
					multiplicative_expression |
					additive_expression '+' multiplicative_expression |
					additive_expression '-' multiplicative_expression

